<div class="min-h-screen bg-white flex flex-col">

  <!-- header with back button -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10 flex-shrink-0">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
      <div class="flex-1 flex justify-start items-center">
        <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
          <img
            src="assets/images/BcLogo.png"
            alt="Benedicto College Logo"
            class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
            onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
            onload="console.log('Logo loaded successfully:', this.src);"
          >
        </a>
      </div>
      <nav class="flex-shrink-0">
        <button
          routerLink="/"
          class="bg-black hover:bg-gray-800 text-white font-semibold py-2 px-4 sm:px-6 rounded-lg transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-sm sm:text-base flex items-center space-x-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <span>Back to Home</span>
        </button>
      </nav>
    </div>
  </header>

  <!-- main content -->
  <main class="flex-1 flex">

    <!-- logo image -->
    <div class="flex items-center justify-center p-8">
      <img
        src="assets/images/login-logo.png"
        alt="Benedicto College Login"
        class="max-w-full h-auto object-fit-contain"
        style="max-height: 500px;"
        onerror="this.src='assets/images/login-logo.jpg'; console.log('Switched to JPG format');"
        onload="console.log('Login logo loaded successfully');"
      >
    </div>

    <!-- login form container -->
    <div class="flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <!-- login card -->
      <div class="bg-white rounded-2xl shadow-xl border-2 border-gray-300 p-5 sm:p-6 w-full max-w-md lg:max-w-lg mx-auto relative overflow-hidden">
            <!-- decorative circles lang -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full opacity-50 -translate-y-16 translate-x-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-50 translate-y-12 -translate-x-12"></div>

            <!-- user icon ug welcome text -->
            <div class="text-center mb-4 sm:mb-5 relative z-10">
             <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 shadow-xl">
  <svg class="w-7 h-7 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
  </svg>
</div>

              <h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-1">Welcome Back!</h2>
              <p class="text-sm text-gray-600">Access your academic resources</p>
            </div>


            <form class="space-y-3 relative z-10" id="loginForm" (submit)="onSubmit($event)">
       
              <div>
                <label for="studentId" class="block text-sm font-semibold text-gray-700 mb-2">
                  Student ID <span class="text-red-500">*</span>
                </label>
 <input
  type="text"
  id="studentId"
  name="studentId"
  placeholder="2000-00000"
  maxlength="10"
  inputmode="numeric"
  (input)="onStudentIdInput($event)"
  (blur)="onStudentIdBlur($event)"
  (keydown)="onStudentIdKeydown($event)"
  (paste)="onStudentIdPaste($event)"
  (drop)="onStudentIdDrop($event)"
  (dragover)="onStudentIdDragOver($event)"
  class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300 bg-white text-gray-900 placeholder-gray-500 text-sm sm:text-base"
/>


            <div id="studentIdError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium hidden bg-red-50 border border-red-200 rounded-lg p-2">
               Student ID must be exactly 9 digits (format: 2000-00000)
            </div>
          </div>

       
          <div>
            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
              Password <span class="text-red-500">*</span>
            </label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="••••••••••"
              class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300 bg-white text-gray-900 placeholder-gray-500 text-sm sm:text-base"
              (input)="onPasswordInput($event)"
              (blur)="onPasswordBlur($event)"
            >
            <div id="passwordError" class="text-red-600 text-xs sm:text-sm mt-2 font-medium hidden bg-red-50 border border-red-200 rounded-lg p-2">
               Password is required!
            </div>
          </div>

          <!-- remember me ug forgot password -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <label class="flex items-center">
              <input
                type="checkbox"
                class="w-4 h-4 text-orange-500 border-2 border-gray-300 rounded focus:ring-orange-200 focus:ring-2 bg-white"
              >
              <span class="ml-3 text-sm font-medium text-gray-700">Remember me</span>
            </label>
            <a href="#" class="text-sm font-semibold text-orange-600 hover:text-orange-700 transition duration-300">
              Forgot your password?
            </a>
          </div>

          <!-- login button -->
          <button
            type="submit"
            class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 sm:py-4 px-4 sm:px-6 rounded-xl transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-blue-200 text-sm sm:text-base"
          >
            Sign In to Your Account
          </button>
        </form>

        <!-- terms and privacy policy -->
        <div class="mt-3 text-center relative z-10">
          <p class="text-xs text-gray-500 leading-relaxed">
            By signing in, you agree to our
            <a routerLink="/terms-of-service" class="text-blue-600 hover:text-blue-700 underline font-medium transition duration-300">Terms of Service</a>
            and
            <a routerLink="/privacy-policy" class="text-blue-600 hover:text-blue-700 underline font-medium transition duration-300">Privacy Policy</a>
          </p>
        </div>

            <!-- help links -->
            <div class="mt-4 sm:mt-5 pt-3 sm:pt-4 border-t border-gray-200 text-center relative z-10">
              <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm">
                <a routerLink="/support" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
                  Need Help?
                </a>
                <span class="text-gray-400 hidden sm:inline">|</span>
                <a routerLink="/support" class="text-gray-600 hover:text-orange-600 font-medium transition duration-300">
                  Request Account Access
                </a>
              </div>
        </div>
      </div>
    </div>

  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="container mx-auto px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Get in Touch</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/privacy-policy" class="block text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Back to Home</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Get in Touch</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/privacy-policy" class="block text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Back to Home</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left text-sm">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-xs text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>


