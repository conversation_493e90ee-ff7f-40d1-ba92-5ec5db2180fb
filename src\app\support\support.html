<div class="min-h-screen support-container relative overflow-hidden">
  <!-- Background pattern para sa support page, friendly ug welcoming design -->
  <div class="absolute inset-0 opacity-6">
    <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
      <defs>
        <pattern id="support-grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="#3b82f6" opacity="0.3"/>
          <circle cx="5" cy="5" r="0.5" fill="#f59e0b" opacity="0.4"/>
          <circle cx="15" cy="15" r="0.5" fill="#0ea5e9" opacity="0.4"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#support-grid)" />
    </svg>
  </div>

  <!-- Header consistent sa design -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
      <div class="flex-1 flex justify-start items-center">
        <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
          <img
            src="assets/images/BcLogo.png"
            alt="Benedicto College Logo"
            class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
          >
        </a>
      </div>
      <nav class="flex-shrink-0">
        <button
          routerLink="/login"
          class="bg-black hover:bg-gray-800 text-white font-semibold py-2 px-4 sm:px-6 rounded-lg transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-sm sm:text-base flex items-center space-x-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <span>Back to Login</span>
        </button>
      </nav>
    </div>
  </header>

  <!-- Main content section para sa support -->
  <main class="relative z-10 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      
      <!-- Support Header -->
      <div class="text-center mb-12">
        <div >
          <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
          </svg>
        </div>
        <h1 class="text-4xl sm:text-5xl font-bold support-header mb-4">Student Support Center</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          We're here to help you make the most of your library experience at Benedicto College. Find answers, get assistance, and access resources.
        </p>
        <div class="support-divider w-32 mx-auto mt-6"></div>
      </div>

      <!-- Emergency Contact -->
      <div class="urgent-box p-6 mb-8">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <h3 class="text-xl font-bold text-gray-800">Priority Support Contact</h3>
        </div>
        <p class="text-gray-700 mb-4">For urgent technical issues or account-related concerns, please contact our support team through the following channels:</p>
        <div class="flex flex-col sm:flex-row gap-4">
          <a href="tel:+***********" class="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 text-center">
            📞 Phone: (*************
          </a>
          <a href="mailto:support&#64;benedictocollege.edu.ph" class="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 text-center">
            ✉️ Email: support&#64;benedictocollege.edu.ph
          </a>
        </div>
      </div>

      <!-- Help Categories -->
      <div class="space-y-8 mb-12">

        <!-- Account & Login Help -->
        <div class="border-l-4 border-orange-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Account & Login</h3>
          <p class="text-gray-600 mb-4">Issues with student ID, password reset, account access, and login problems.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Forgot password recovery</li>
            <li>• Student ID format help</li>
            <li>• Account activation</li>
            <li>• Login troubleshooting</li>
          </ul>
        </div>

        <!-- Library Resources -->
        <div class="border-l-4 border-orange-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Library Resources</h3>
          <p class="text-gray-600 mb-4">Help with finding books, digital resources, and research materials.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Book search and reservation</li>
            <li>• Digital database access</li>
            <li>• Research assistance</li>
            <li>• Citation help</li>
          </ul>
        </div>

        <!-- Technical Support -->
        <div class="border-l-4 border-orange-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Technical Support</h3>
          <p class="text-gray-600 mb-4">System issues, browser problems, and technical troubleshooting.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Browser compatibility</li>
            <li>• System performance</li>
            <li>• Mobile app support</li>
            <li>• Connection issues</li>
          </ul>
        </div>

        <!-- Borrowing & Returns -->
        <div class="border-l-4 border-orange-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Borrowing & Returns</h3>
          <p class="text-gray-600 mb-4">Questions about borrowing policies, due dates, and renewals.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Borrowing limits and policies</li>
            <li>• Renewal procedures</li>
            <li>• Overdue fines</li>
            <li>• Return procedures</li>
          </ul>
        </div>

        <!-- Study Spaces -->
        <div class="border-l-4 border-orange-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Study Spaces</h3>
          <p class="text-gray-600 mb-4">Information about study rooms, computer labs, and facility reservations.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Room reservations</li>
            <li>• Computer lab access</li>
            <li>• Group study areas</li>
            <li>• Facility guidelines</li>
          </ul>
        </div>

        <!-- General Inquiries -->
        <div class="border-l-4 border-orange-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">General Inquiries</h3>
          <p class="text-gray-600 mb-4">Other questions, feedback, and general library information.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Library hours and policies</li>
            <li>• Services and programs</li>
            <li>• Feedback and suggestions</li>
            <li>• General information</li>
          </ul>
        </div>

      </div>

      <!-- Contact Information -->
      <div class="space-y-8 mb-12">

        <!-- Primary Support -->
        <div class="border-l-4 border-blue-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Primary Support</h3>
          <div class="space-y-3">
            <div>
              <p class="font-medium text-gray-900">Email Support</p>
              <p class="text-gray-600">support&#64;benedictocollege.edu.ph</p>
            </div>
            <div>
              <p class="font-medium text-gray-900">Phone Support</p>
              <p class="text-gray-600">(*************</p>
            </div>
            <div>
              <p class="font-medium text-gray-900">Support Hours</p>
              <p class="text-gray-600">Monday-Friday: 8:00 AM - 5:00 PM</p>
            </div>
          </div>
        </div>

        <!-- Walk-in Support -->
        <div class="border-l-4 border-blue-500 pl-6 py-4">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Walk-in Support</h3>
          <div class="space-y-3">
            <div>
              <p class="font-medium text-gray-900">Help Desk Location</p>
              <p class="text-gray-600">Library Main Floor, Information Desk</p>
            </div>
            <div>
              <p class="font-medium text-gray-900">Walk-in Hours</p>
              <p class="text-gray-600">Monday-Saturday: 7:00 AM - 8:00 PM</p>
            </div>
            <div>
              <p class="font-medium text-gray-900">Staff Available</p>
              <p class="text-gray-600">Librarians & IT Support Specialists</p>
            </div>
          </div>
        </div>

      </div>

      <!-- Quick Tips -->
      <div class="border-l-4 border-green-500 pl-6 py-4">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Tips for Faster Support</h3>
        <ul class="text-gray-700 space-y-2">
          <li>• Have your Student ID ready when contacting support</li>
          <li>• Describe the specific error message or issue you're experiencing</li>
          <li>• Try clearing your browser cache before reporting technical issues</li>
          <li>• Check your internet connection for access problems</li>
          <li>• Include screenshots when reporting visual issues via email</li>
        </ul>
      </div>

    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500 relative z-10 mt-12">
    <div class="container mx-auto px-4 sm:px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/privacy-policy" class="block text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-orange-400 font-medium">Support</a>
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Back to Home</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+***********" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Get in Touch</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/privacy-policy" class="block text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-orange-400 font-medium">Support</a>
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Back to Home</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+***********" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button
    id="scrollToTopBtn"
    onclick="scrollToTop()"
    class="fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 z-50 opacity-0 invisible"
    title="Scroll to top"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
    </svg>
  </button>
</div>

<script>
  // Scroll to Top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // Show/hide scroll to top button based on scroll position
  window.addEventListener('scroll', function() {
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (window.pageYOffset > 300) {
      scrollToTopBtn.classList.remove('opacity-0', 'invisible');
      scrollToTopBtn.classList.add('opacity-100', 'visible');
    } else {
      scrollToTopBtn.classList.remove('opacity-100', 'visible');
      scrollToTopBtn.classList.add('opacity-0', 'invisible');
    }
  });
</script>
