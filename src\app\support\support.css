/* Support Page Styles - Benedicto College Library Management System */

.support-container {
  background: white;
}

.support-card {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.support-header {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.support-divider {
  background: linear-gradient(90deg, #0ea5e9 0%, #3b82f6 25%, #f59e0b 75%, #f97316 100%);
  height: 4px;
  border-radius: 2px;
}

.help-category {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.help-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.contact-card {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 5px solid #3b82f6;
}

.faq-item {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-left: 3px solid #f59e0b;
}

.support-icon {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
}

.category-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

.urgent-box {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-left: 5px solid #ef4444;
}

.success-tip {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-left: 5px solid #22c55e;
}

@media (max-width: 768px) {
  .support-card {
    margin: 0.5rem;
    padding: 1.5rem;
  }
  
  .help-category {
    margin-bottom: 1rem;
  }
  
  .support-header {
    font-size: 2rem;
  }
}
