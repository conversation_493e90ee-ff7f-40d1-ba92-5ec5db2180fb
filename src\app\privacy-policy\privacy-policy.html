<div class="min-h-screen bg-white">

  <!-- Header consistent sa design sa login ug landing page -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
      <div class="flex-1 flex justify-start items-center">
        <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
          <img
            src="assets/images/BcLogo.png"
            alt="Benedicto College Logo"
            class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
          >
        </a>
      </div>
      <nav class="flex-shrink-0">
        <button
          routerLink="/login"
          class="bg-black hover:bg-gray-800 text-white font-semibold py-2 px-4 sm:px-6 rounded-lg transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-sm sm:text-base flex items-center space-x-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <span>Back to Login</span>
        </button>
      </nav>
    </div>
  </header>

  <!-- Main content section para sa privacy policy -->
  <main class="py-12 px-6 sm:px-8 lg:px-12">
      <!-- Privacy Policy Content -->
        
        <!-- Header section with icon ug title -->
        <div class="text-center mb-12">
          <div class="w-20 h-20 info-icon rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h1 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Protecting your personal information and academic data is our top priority at Benedicto College Library Management System.
          </p>
          <div class="section-divider w-24 mx-auto mt-6"></div>
        </div>

        <!-- Last Updated info -->
        <div class="highlight-box rounded-xl p-6 mb-8">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-semibold text-gray-800">Last Updated: January 2025</span>
          </div>
        </div>

        <!-- Privacy Policy Content -->
        <div class="space-y-8">
          
          <!-- Information We Collect Section -->
          <section>
            <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <span class="w-8 h-8 bg-blue-500 text-white rounded-lg flex items-center justify-center mr-3 text-sm font-bold">1</span>
              Information We Collect
            </h2>
            <div class="bg-gray-50 rounded-xl p-6 space-y-4">
              <h3 class="text-lg font-semibold text-gray-800">Student Information:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Student ID number and academic credentials</li>
                <li>Full name, email address, and contact information</li>
                <li>Academic program and year level</li>
                <li>Library usage history and borrowing records</li>
                <li>Digital resource access logs and preferences</li>
              </ul>
              
              <h3 class="text-lg font-semibold text-gray-800 mt-6">Technical Information:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Device information and browser type</li>
                <li>IP address and location data (for security purposes)</li>
                <li>System usage analytics and performance data</li>
                <li>Login timestamps and session information</li>
              </ul>
            </div>
          </section>

          <!-- How We Use Information Section -->
          <section>
            <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <span class="w-8 h-8 bg-green-500 text-white rounded-lg flex items-center justify-center mr-3 text-sm font-bold">2</span>
              How We Use the Information We Collect
            </h2>
            <div class="bg-gray-50 rounded-xl p-6 space-y-4">
              <h3 class="text-lg font-semibold text-gray-800">Academic Services:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Provide access to library resources and digital materials</li>
                <li>Manage book reservations, renewals, and return notifications</li>
                <li>Track academic research progress and resource utilization</li>
                <li>Generate usage reports for academic planning</li>
              </ul>
              
              <h3 class="text-lg font-semibold text-gray-800 mt-6">System Operations:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Maintain system security and prevent unauthorized access</li>
                <li>Improve user experience and system performance</li>
                <li>Send important notifications about library services</li>
                <li>Comply with educational institution requirements</li>
              </ul>
            </div>
          </section>

          <!-- Privacy Obligations Section -->
          <section>
            <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <span class="w-8 h-8 bg-purple-500 text-white rounded-lg flex items-center justify-center mr-3 text-sm font-bold">3</span>
              Your Privacy Obligations
            </h2>
            <div class="bg-gray-50 rounded-xl p-6 space-y-4">
              <h3 class="text-lg font-semibold text-gray-800">Student Responsibilities:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Keep your login credentials secure and confidential</li>
                <li>Report any unauthorized access to your account immediately</li>
                <li>Use library resources only for legitimate academic purposes</li>
                <li>Respect intellectual property rights and copyright laws</li>
                <li>Update your contact information when necessary</li>
              </ul>
              
              <h3 class="text-lg font-semibold text-gray-800 mt-6">Data Protection:</h3>
              <ul class="list-disc list-inside space-y-2 text-gray-700 ml-4">
                <li>Do not share your account with other students</li>
                <li>Log out properly when using shared computers</li>
                <li>Report any privacy concerns to the library administration</li>
                <li>Follow Benedicto College's acceptable use policies</li>
              </ul>
            </div>
          </section>

        </div>

        <!-- Contact Information -->
        <div class="mt-12 p-6 bg-white rounded-xl border border-gray-200">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Questions About This Privacy Policy?</h3>
          <p class="text-gray-700 mb-4">
            If you have any questions or concerns about how we handle your personal information, please contact us:
          </p>
          <div class="space-y-2 text-gray-700">
            <p><strong>Email:</strong> library&#64;benedictocollege.edu.ph</p>
            <p><strong>Phone:</strong> (*************</p>
            <p><strong>Office:</strong> Benedicto College Library Administration</p>
          </div>
        </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/privacy-policy" class="block text-orange-400 font-medium">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Back to Home</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Get in Touch</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/privacy-policy" class="block text-orange-400 font-medium">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Back to Home</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button
    id="scrollToTopBtn"
    onclick="scrollToTop()"
    class="fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 z-50 opacity-0 invisible"
    title="Scroll to top"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
    </svg>
  </button>
</div>

<script>
  // Scroll to Top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // Show/hide scroll to top button based on scroll position
  window.addEventListener('scroll', function() {
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (window.pageYOffset > 300) {
      scrollToTopBtn.classList.remove('opacity-0', 'invisible');
      scrollToTopBtn.classList.add('opacity-100', 'visible');
    } else {
      scrollToTopBtn.classList.remove('opacity-100', 'visible');
      scrollToTopBtn.classList.add('opacity-0', 'invisible');
    }
  });
</script>


